import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../hooks/useAuth';
import {
  VideoCameraIcon,
  ClockIcon,
  CalendarIcon,
  UserIcon,
  CheckCircleIcon,
  XCircleIcon,
  BellIcon,
  ChartBarIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  ArrowDownTrayIcon,
  StarIcon,
  PlayCircleIcon,
  PaperClipIcon,
  DocumentArrowDownIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { format, parseISO } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Link } from 'react-router-dom';
import api from '../../../services/api';
import toast from 'react-hot-toast';

/**
 * <PERSON><PERSON>şan görüşmeleri sayfası
 */
const ClientSessionsPage = () => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [sessions, setSessions] = useState([]);
  const [activeTab, setActiveTab] = useState('upcoming'); // upcoming, past, all
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  // Mock görüşme durumları
  const sessionStatuses = {
    scheduled: "Planlandı",
    inProgress: "Devam Ediyor",
    completed: "Tamamlandı",
    missed: "Kaçırıldı",
    cancelled: "İptal Edildi",
  };

  // Seans durumunu belirle
  const getSessionStatus = (appointment) => {
    try {
      const now = new Date();
      const appointmentTime = appointment.appointmentDate ? new Date(appointment.appointmentDate) : null;
      const endTime = appointment.endTime ? new Date(appointment.endTime) : null;

      // Tarih parse edilemezse varsayılan durum
      if (!appointmentTime || isNaN(appointmentTime.getTime())) {
        console.warn('Invalid appointment date:', appointment.appointmentDate);
        return 'scheduled';
      }

      if (!endTime || isNaN(endTime.getTime())) {
        console.warn('Invalid end time:', appointment.endTime);
        return 'scheduled';
      }

      if (now < appointmentTime) {
        return 'scheduled'; // Planlandı
      } else if (now >= appointmentTime && now <= endTime) {
        return 'inProgress'; // Devam ediyor
      } else {
        return 'completed'; // Tamamlandı
      }
    } catch (error) {
      console.error('Error in getSessionStatus:', error);
      return 'scheduled';
    }
  };

  useEffect(() => {
    loadSessions();
  }, []);

  const loadSessions = async () => {
    try {
      setIsLoading(true);
      console.log('🔄 Client Sessions: API çağrısı başlatılıyor...');

      // Confirmed appointments'ları sessions olarak göster
      const response = await api.get('/clients/appointments');
      console.log('📡 Client Sessions: API response:', response.data);

      const appointments = response.data.appointments || [];
      console.log('📋 Client Sessions: Appointments:', appointments);

      // Sadece confirmed appointments'ları sessions olarak göster
      const confirmedSessions = appointments
        .filter(apt => {
          console.log(`🔍 Filtering appointment ${apt.id}: status = ${apt.status}`);
          return apt.status === 'confirmed' || apt.status === 'Confirmed';
        })
        .sort((a, b) => new Date(a.appointmentDate) - new Date(b.appointmentDate)) // Tarihe göre sırala
        .map((apt, index, sortedAppointments) => {
          console.log('🔄 Mapping appointment to session:', apt);

          // Tarih formatlarını güvenli şekilde parse et
          const startTime = apt.appointmentDate ? new Date(apt.appointmentDate) : null;
          const endTime = apt.endTime ? new Date(apt.endTime) : null;

          console.log('📅 Date parsing:', {
            appointmentDate: apt.appointmentDate,
            endTime: apt.endTime,
            parsedStartTime: startTime,
            parsedEndTime: endTime
          });

          // Aynı uzmanla bu seansa kadar kaç seans yapıldığını hesapla
          const sessionsWithSameExpert = sortedAppointments
            .slice(0, index + 1) // Bu seans dahil, önceki seanslar
            .filter(prevApt => prevApt.expertId === apt.expertId);
          const sessionNumber = sessionsWithSameExpert.length;

          return {
            id: apt.id,
            appointmentId: apt.id,
            expertId: apt.expertId,
            expertName: apt.expertName,
            expertTitle: apt.expertTitle || 'Uzman',
            expertSpecialty: apt.expertTitle || 'Uzman',
            expertAvatar: apt.expertAvatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(apt.expertName || 'Uzman')}&background=4f46e5&color=fff&size=200&font-size=0.6`,
            date: apt.date || (startTime ? startTime.toISOString().split('T')[0] : null),
            startTime: apt.startTime || (startTime ? startTime.toTimeString().slice(0, 5) : ''),
            endTime: apt.endTime || (endTime ? endTime.toTimeString().slice(0, 5) : ''),
            startTimeObj: startTime,
            endTimeObj: endTime,
            duration: apt.duration || 50,
            status: getSessionStatus(apt),
            notes: apt.notes || '',
            meetingLink: apt.meetingLink,
            packageName: apt.notes || 'Danışmanlık Seansı',
            sessionNumber: sessionNumber,
            createdAt: apt.createdAt ? new Date(apt.createdAt) : null
          };
        });

      console.log('✅ Client Sessions: Confirmed sessions:', confirmedSessions);
      setSessions(confirmedSessions);
    } catch (error) {
      console.error('❌ Client Sessions: Hata:', error);
      toast.error('Seanslar yüklenemedi');
    } finally {
      setIsLoading(false);
    }
  };

  // İstatistik hesaplamaları
  const stats = {
    total: sessions.length,
    upcoming: sessions.filter(s => s.status === 'scheduled').length,
    completed: sessions.filter(s => s.status === 'completed').length,
    missed: sessions.filter(s => s.status === 'missed').length,
    cancelled: sessions.filter(s => s.status === 'cancelled').length
  };

  // Bugünün tarihi
  const today = new Date();
  
  // Görüşmeleri filtrele
  const filteredSessions = sessions.filter(session => {
    // Tam tarih-saat kullan (sadece tarih değil)
    const sessionDateTime = session.startTimeObj || new Date(session.appointmentDate);
    const now = new Date();

    console.log('🔍 Session filtering:', {
      sessionId: session.id,
      sessionDateTime: sessionDateTime,
      now: now,
      isUpcoming: sessionDateTime > now,
      status: session.status,
      activeTab: activeTab
    });

    // Tab filtresi
    if (activeTab === 'upcoming') {
      if (!(sessionDateTime > now && session.status === 'scheduled')) {
        return false;
      }
    } else if (activeTab === 'past') {
      if (!(sessionDateTime <= now || session.status === 'completed' || session.status === 'missed' || session.status === 'cancelled')) {
        return false;
      }
    }
    // activeTab === 'all' için herhangi bir filtreleme yapmıyoruz
    
    // Durum filtresi
    if (filterStatus !== 'all' && session.status !== filterStatus) {
      return false;
    }

    // Arama filtresi
    if (searchTerm && !session.expertName.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }

    return true;
  });

  // Tarihe göre sırala
  const sortedSessions = [...filteredSessions].sort((a, b) => {
    // Önce tarihleri karşılaştır
    const dateComparison = new Date(a.date) - new Date(b.date);
    if (dateComparison !== 0) return dateComparison;
    
    // Tarihler aynıysa başlama saatini karşılaştır
    return a.startTime.localeCompare(b.startTime);
  });

  // Durum badge renkleri
  const getStatusBadge = (status) => {
    switch (status) {
      case 'scheduled':
        return 'bg-teal-100 text-teal-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'missed':
        return 'bg-amber-100 text-amber-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Border renkleri
  const getStatusBorder = (status) => {
    switch (status) {
      case 'scheduled':
        return 'border-teal-500';
      case 'completed':
        return 'border-green-500';
      case 'missed':
        return 'border-amber-500';
      case 'cancelled':
        return 'border-red-500';
      default:
        return 'border-gray-500';
    }
  };

  // Yükleniyor durumu
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-600"></div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen pb-12">
      <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8 pt-4 sm:pt-8">
        {/* Başlık ve Üst Kısım */}
        <div className="bg-gradient-to-r from-purple-500 to-purple-600 shadow-lg rounded-lg p-4 sm:p-6 mb-4 sm:mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
            <div>
              <h1 className="text-xl sm:text-2xl font-bold text-white">Seanslarım</h1>
              <p className="mt-1 text-purple-100 text-sm sm:text-base">
                Tamamlanan seanslarınızı ve notlarınızı buradan görüntüleyebilirsiniz
              </p>
            </div>
            <div className="mt-3 sm:mt-0 flex space-x-2">
              <Link
                to="/client/experts"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-colors duration-150 whitespace-nowrap"
              >
                <UserIcon className="h-4 w-4 mr-2" />
                Yeni Seans
              </Link>
              <Link
                to="/client/appointments"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-purple-600 bg-white hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-150 whitespace-nowrap"
              >
                <CalendarIcon className="h-4 w-4 mr-2" />
                Randevularım
              </Link>
              <Link
                to="/client/messages"
                className="inline-flex items-center px-4 py-2 border border-white text-sm font-medium rounded-md shadow-sm text-white hover:bg-purple-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-150 whitespace-nowrap"
              >
                <ChatBubbleLeftRightIcon className="h-4 w-4 mr-2" />
                Mesajlarım
              </Link>
            </div>
          </div>
        </div>

        {/* Özet İstatistikler */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
          <div 
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-teal-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'all' ? 'ring-2 ring-teal-500' : ''}`}
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('all');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Toplam</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.total}</span>
          </div>
          
          <div 
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-teal-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'upcoming' && filterStatus === 'scheduled' ? 'ring-2 ring-teal-500' : ''}`}
            onClick={() => {
              setActiveTab('upcoming');
              setFilterStatus('scheduled');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Planlanan</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.upcoming}</span>
          </div>
          
          <div 
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-green-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'completed' ? 'ring-2 ring-green-500' : ''}`}
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('completed');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Tamamlanan</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.completed}</span>
          </div>
          
          <div 
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-amber-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'missed' ? 'ring-2 ring-amber-500' : ''}`}
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('missed');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Kaçırılan</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.missed}</span>
          </div>
          
          <div 
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-red-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'cancelled' ? 'ring-2 ring-red-500' : ''}`}
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('cancelled');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">İptal Edilen</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.cancelled}</span>
          </div>
        </div>

        {/* Ana Sekme Navigasyonu */}
        <div className="overflow-x-auto scrollbar-hide border-b border-gray-200 mb-6">
          <div className="flex min-w-max">
            <button
              onClick={() => {
                setActiveTab('upcoming');
                setFilterStatus('scheduled');
              }}
              className={`py-4 px-4 sm:px-6 text-center border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === 'upcoming'
                  ? 'border-teal-500 text-teal-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center">
                <CalendarIcon className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2" />
                <span className="text-xs sm:text-sm">Yaklaşan</span>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('past')}
              className={`py-4 px-4 sm:px-6 text-center border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === 'past'
                  ? 'border-teal-500 text-teal-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center">
                <CheckCircleIcon className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2" />
                <span className="text-xs sm:text-sm">Geçmiş</span>
              </div>
            </button>
            <button
              onClick={() => {
                setActiveTab('all');
                setFilterStatus('all');
              }}
              className={`py-4 px-4 sm:px-6 text-center border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === 'all'
                  ? 'border-teal-500 text-teal-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center">
                <DocumentTextIcon className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2" />
                <span className="text-xs sm:text-sm">Tümü</span>
              </div>
            </button>
          </div>
        </div>

        {/* Arama */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="px-4 py-5 sm:p-6">
            <div className="max-w-lg">
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                  placeholder="Uzman adına göre ara..."
                />
              </div>
            </div>
          </div>
        </div>

        {/* Görüşmeler Listesi */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">
              {activeTab === 'upcoming' ? 'Yaklaşan Seanslar' :
               activeTab === 'past' ? 'Geçmiş Seanslar' : 'Tüm Seanslar'}
              {filterStatus !== 'all' && ` - ${sessionStatuses[filterStatus]}`}
            </h2>
          </div>

          {sortedSessions.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {sortedSessions.map((session) => (
                <div key={session.id} className="p-4 sm:p-6 hover:bg-gray-50 transition duration-150">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                    <div className="flex items-center space-x-3 min-w-0 flex-1">
                      <div className="flex-shrink-0">
                        <img
                          className="h-8 w-8 sm:h-10 sm:w-10 rounded-full border border-gray-200"
                          src={session.expertAvatar}
                          alt={session.expertName}
                        />
                      </div>
                      <div className="min-w-0 flex-1">
                        <h3 className="text-sm font-medium text-gray-900 truncate">{session.expertName}</h3>
                        <p className="text-xs text-gray-500 truncate">{session.expertTitle}</p>
                        <div className="flex flex-wrap gap-1 sm:gap-2 text-xs text-gray-500 mt-1">
                          {session.startTimeObj ? (
                            <>
                              <span>{format(session.startTimeObj, 'EEEE', { locale: tr })}</span>
                              <span>•</span>
                              <span>{format(session.startTimeObj, 'd MMMM yyyy', { locale: tr })}</span>
                            </>
                          ) : session.date ? (
                            <>
                              <span>{format(parseISO(session.date), 'EEEE', { locale: tr })}</span>
                              <span>•</span>
                              <span>{format(parseISO(session.date), 'd MMMM yyyy', { locale: tr })}</span>
                            </>
                          ) : (
                            <span>Tarih bilgisi yok</span>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-2">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadge(session.status)}`}>
                        {sessionStatuses[session.status]}
                      </span>
                      <span className="text-xs text-gray-500 truncate">{session.packageName}</span>
                    </div>
                  </div>

                  <div className="mt-4 flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                    <div className="flex flex-col sm:flex-row sm:space-x-6 space-y-2 sm:space-y-0 text-sm text-gray-500">
                      <div className="flex items-center">
                        <ClockIcon className="h-4 w-4 text-gray-400 mr-1.5" />
                        <span>{session.startTime} - {session.endTime}</span>
                      </div>
                      <div className="flex items-center">
                        <UserIcon className="h-4 w-4 text-gray-400 mr-1.5" />
                        <span>Seans #{session.sessionNumber}</span>
                      </div>
                      <div className="flex items-center">
                        <VideoCameraIcon className="h-4 w-4 text-gray-400 mr-1.5" />
                        <span>Video Görüşme</span>
                      </div>
                    </div>

                    <div className="overflow-x-auto scrollbar-hide">
                      <div className="flex gap-2 min-w-max pb-2">
                      {session.status === 'scheduled' && session.meetingLink && (
                        <button
                          type="button"
                          onClick={() => {
                            console.log('🚀 P2P Meeting sayfasına yönlendiriliyor, randevu:', session.id);
                            window.open(`/meeting/${session.id}`, '_blank');
                          }}
                          className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                        >
                          <PlayCircleIcon className="-ml-0.5 mr-1 h-4 w-4" />
                          Görüşmeye Katıl
                        </button>
                      )}

                      {session.status === 'scheduled' && !session.meetingLink && (
                        <div className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-500 bg-gray-50">
                          <VideoCameraIcon className="-ml-0.5 mr-1 h-4 w-4" />
                          Görüşme Odası Hazırlanıyor
                        </div>
                      )}
                      
                      {session.recordingAvailable && (
                        <button
                          type="button"
                          className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-teal-700 bg-teal-100 hover:bg-teal-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                        >
                          <DocumentArrowDownIcon className="-ml-0.5 mr-1 h-4 w-4" />
                          Kaydı İndir
                        </button>
                      )}
                      
                      {(session.status === 'completed' || session.status === 'missed') && (
                        <Link
                          to={`/client/sessions/${session.id}/notes`}
                          className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                        >
                          <DocumentTextIcon className="-ml-0.5 mr-1 h-4 w-4" />
                          Seans Notları
                        </Link>
                      )}
                      
                      <Link
                        to={`/client/messages?expert=${session.expertId}`}
                        className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                      >
                        <ChatBubbleLeftRightIcon className="-ml-0.5 mr-1 h-4 w-4" />
                        Uzmana Mesaj
                      </Link>
                      
                      <Link
                        to={`/client/experts/${session.expertId}`}
                        className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                      >
                        <UserIcon className="-ml-0.5 mr-1 h-4 w-4" />
                        Uzman Profili
                      </Link>
                      </div>
                    </div>
                  </div>

                  {session.notes && (
                    <div className="mt-2 text-xs text-gray-500 bg-gray-50 px-3 py-1.5 rounded-md">
                      <span className="font-medium">Not:</span> {session.notes}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="py-12 text-center">
              <CalendarIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Seans Bulunamadı</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm || filterStatus !== 'all'
                  ? 'Arama kriterlerinize uygun seans bulunamadı.'
                  : 'Henüz bir seansınız bulunmuyor.'}
              </p>
              <div className="mt-6">
                <Link
                  to="/client/experts"
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700 focus:outline-none"
                >
                  <UserIcon className="h-4 w-4 mr-2" />
                  Uzman Ara
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ClientSessionsPage; 