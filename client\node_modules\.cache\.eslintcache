[{"C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\index.jsx": "1", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\App.jsx": "2", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\contexts\\AuthContext.jsx": "3", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\hooks\\useAuth.js": "4", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\PagePermission.jsx": "5", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ProtectedRoute.jsx": "6", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\PageTransition.jsx": "7", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\layout\\MainLayout.jsx": "8", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\layout\\AuthLayout.jsx": "9", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\services\\api.js": "10", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\LoginPage.jsx": "11", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\RegisterPage.jsx": "12", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\ForgotPasswordPage.jsx": "13", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\dashboard\\DashboardPage.jsx": "14", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\profile\\ProfilePage.jsx": "15", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\dashboard\\ExpertDashboardPage.jsx": "16", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\profile\\ExpertProfilePage.jsx": "17", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\users\\UsersPage.jsx": "18", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\profile\\ClientProfilePage.jsx": "19", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\users\\UserFormPage.jsx": "20", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RolesPage.jsx": "21", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RoleFormPage.jsx": "22", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\permissions\\PermissionsPage.jsx": "23", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RolePermissionsPage.jsx": "24", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\audit-logs\\AuditLogsPage.jsx": "25", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\settings\\SystemSettingsPage.jsx": "26", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\profile\\AdminProfilePage.jsx": "27", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\availabilities\\index.js": "28", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\sessions\\index.js": "29", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\appointments\\index.js": "30", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\messages\\index.js": "31", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\reports\\index.js": "32", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\clients\\index.js": "33", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\dashboard\\index.js": "34", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\index.js": "35", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\index.js": "36", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\messages\\index.js": "37", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\sessions\\index.js": "38", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\packages\\index.js": "39", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\payments\\index.js": "40", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\availabilities\\AvailabilityPage.jsx": "41", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\sessions\\SessionsPage.jsx": "42", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\appointments\\AppointmentsPage.jsx": "43", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\messages\\MessagesPage.jsx": "44", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\reports\\ReportsPage.jsx": "45", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\clients\\ClientsPage.jsx": "46", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\components\\PermissionMatrix.jsx": "47", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\ClientAppointmentsPage.jsx": "48", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\dashboard\\ClientDashboardPage.jsx": "49", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\ClientExpertsPage.jsx": "50", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\messages\\ClientMessagesPage.jsx": "51", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\index.js": "52", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\packages\\ClientPackagesPage.jsx": "53", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\sessions\\ClientSessionsPage.jsx": "54", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\payments\\ClientPaymentsPage.jsx": "55", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormTextarea.jsx": "56", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormSelect.jsx": "57", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormRadio.jsx": "58", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormCheckbox.jsx": "59", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormInput.jsx": "60", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\common\\MultiSelect.jsx": "61", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\ClientExpertDetailPage.jsx": "62", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\BookAppointmentPage.jsx": "63", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\notifications\\NotificationCenter.jsx": "64", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\meeting\\MeetingPage.jsx": "65", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\common\\ScrollToTop.jsx": "66"}, {"size": 1000, "mtime": 1755275190474, "results": "67", "hashOfConfig": "68"}, {"size": 13093, "mtime": 1755031329574, "results": "69", "hashOfConfig": "68"}, {"size": 12396, "mtime": 1755030101660, "results": "70", "hashOfConfig": "68"}, {"size": 283, "mtime": 1742651186300, "results": "71", "hashOfConfig": "68"}, {"size": 1594, "mtime": 1755031392192, "results": "72", "hashOfConfig": "68"}, {"size": 808, "mtime": 1742651192925, "results": "73", "hashOfConfig": "68"}, {"size": 813, "mtime": 1743121345478, "results": "74", "hashOfConfig": "68"}, {"size": 26669, "mtime": 1755127611624, "results": "75", "hashOfConfig": "68"}, {"size": 840, "mtime": 1742651199783, "results": "76", "hashOfConfig": "68"}, {"size": 4044, "mtime": 1754906950669, "results": "77", "hashOfConfig": "68"}, {"size": 5161, "mtime": 1743023176067, "results": "78", "hashOfConfig": "68"}, {"size": 13594, "mtime": 1743023175878, "results": "79", "hashOfConfig": "68"}, {"size": 3680, "mtime": 1742839996729, "results": "80", "hashOfConfig": "68"}, {"size": 6149, "mtime": 1742652514170, "results": "81", "hashOfConfig": "68"}, {"size": 10004, "mtime": 1742687855831, "results": "82", "hashOfConfig": "68"}, {"size": 26013, "mtime": 1755125754189, "results": "83", "hashOfConfig": "68"}, {"size": 34266, "mtime": 1754517675551, "results": "84", "hashOfConfig": "68"}, {"size": 7579, "mtime": 1742681038274, "results": "85", "hashOfConfig": "68"}, {"size": 18240, "mtime": 1743121346919, "results": "86", "hashOfConfig": "68"}, {"size": 13581, "mtime": 1742729246617, "results": "87", "hashOfConfig": "68"}, {"size": 9374, "mtime": 1742685959799, "results": "88", "hashOfConfig": "68"}, {"size": 21513, "mtime": 1743085705486, "results": "89", "hashOfConfig": "68"}, {"size": 5307, "mtime": 1742686008746, "results": "90", "hashOfConfig": "68"}, {"size": 915, "mtime": 1742668681019, "results": "91", "hashOfConfig": "68"}, {"size": 10152, "mtime": 1742686053807, "results": "92", "hashOfConfig": "68"}, {"size": 16081, "mtime": 1742653511611, "results": "93", "hashOfConfig": "68"}, {"size": 183, "mtime": 1754672350696, "results": "94", "hashOfConfig": "68"}, {"size": 87, "mtime": 1742911290814, "results": "95", "hashOfConfig": "68"}, {"size": 75, "mtime": 1742926306611, "results": "96", "hashOfConfig": "68"}, {"size": 87, "mtime": 1742924113827, "results": "97", "hashOfConfig": "68"}, {"size": 75, "mtime": 1742928519256, "results": "98", "hashOfConfig": "68"}, {"size": 72, "mtime": 1742930957065, "results": "99", "hashOfConfig": "68"}, {"size": 72, "mtime": 1742930563398, "results": "100", "hashOfConfig": "68"}, {"size": 96, "mtime": 1742942293117, "results": "101", "hashOfConfig": "68"}, {"size": 189, "mtime": 1754672075729, "results": "102", "hashOfConfig": "68"}, {"size": 195, "mtime": 1754673752486, "results": "103", "hashOfConfig": "68"}, {"size": 93, "mtime": 1742952326351, "results": "104", "hashOfConfig": "68"}, {"size": 93, "mtime": 1742951288236, "results": "105", "hashOfConfig": "68"}, {"size": 93, "mtime": 1743107128763, "results": "106", "hashOfConfig": "68"}, {"size": 91, "mtime": 1743107140295, "results": "107", "hashOfConfig": "68"}, {"size": 36969, "mtime": 1755276498851, "results": "108", "hashOfConfig": "68"}, {"size": 23758, "mtime": 1755127381098, "results": "109", "hashOfConfig": "68"}, {"size": 23689, "mtime": 1755127462151, "results": "110", "hashOfConfig": "68"}, {"size": 36791, "mtime": 1755126750894, "results": "111", "hashOfConfig": "68"}, {"size": 33648, "mtime": 1742995680878, "results": "112", "hashOfConfig": "68"}, {"size": 19462, "mtime": 1754920395097, "results": "113", "hashOfConfig": "68"}, {"size": 8841, "mtime": 1742669558119, "results": "114", "hashOfConfig": "68"}, {"size": 22547, "mtime": 1755275383010, "results": "115", "hashOfConfig": "68"}, {"size": 25911, "mtime": 1755125782019, "results": "116", "hashOfConfig": "68"}, {"size": 30302, "mtime": 1755125254079, "results": "117", "hashOfConfig": "68"}, {"size": 39346, "mtime": 1755126790087, "results": "118", "hashOfConfig": "68"}, {"size": 295, "mtime": 1743101819804, "results": "119", "hashOfConfig": "68"}, {"size": 23208, "mtime": 1742991622871, "results": "120", "hashOfConfig": "68"}, {"size": 26717, "mtime": 1755275892305, "results": "121", "hashOfConfig": "68"}, {"size": 40654, "mtime": 1742991622874, "results": "122", "hashOfConfig": "68"}, {"size": 3969, "mtime": 1742671375144, "results": "123", "hashOfConfig": "68"}, {"size": 4340, "mtime": 1742671396113, "results": "124", "hashOfConfig": "68"}, {"size": 2654, "mtime": 1742671423203, "results": "125", "hashOfConfig": "68"}, {"size": 2614, "mtime": 1742671410454, "results": "126", "hashOfConfig": "68"}, {"size": 4148, "mtime": 1742671355716, "results": "127", "hashOfConfig": "68"}, {"size": 4754, "mtime": 1754517017782, "results": "128", "hashOfConfig": "68"}, {"size": 18752, "mtime": 1755126588749, "results": "129", "hashOfConfig": "68"}, {"size": 16540, "mtime": 1755028366019, "results": "130", "hashOfConfig": "68"}, {"size": 14604, "mtime": 1755124917531, "results": "131", "hashOfConfig": "68"}, {"size": 21120, "mtime": 1755111866747, "results": "132", "hashOfConfig": "68"}, {"size": 1188, "mtime": 1755127478998, "results": "133", "hashOfConfig": "68"}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "t9pu2d", {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\index.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\App.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\contexts\\AuthContext.jsx", ["332"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\hooks\\useAuth.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\PagePermission.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ProtectedRoute.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\PageTransition.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\layout\\MainLayout.jsx", ["333"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\layout\\AuthLayout.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\services\\api.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\LoginPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\RegisterPage.jsx", ["334"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\ForgotPasswordPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\dashboard\\DashboardPage.jsx", ["335", "336", "337"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\profile\\ProfilePage.jsx", ["338"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\dashboard\\ExpertDashboardPage.jsx", ["339", "340", "341", "342", "343"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\profile\\ExpertProfilePage.jsx", ["344"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\users\\UsersPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\profile\\ClientProfilePage.jsx", ["345", "346"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\users\\UserFormPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RolesPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RoleFormPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\permissions\\PermissionsPage.jsx", ["347", "348"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RolePermissionsPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\audit-logs\\AuditLogsPage.jsx", ["349"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\settings\\SystemSettingsPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\profile\\AdminProfilePage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\availabilities\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\sessions\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\appointments\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\messages\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\reports\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\clients\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\dashboard\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\messages\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\sessions\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\packages\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\payments\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\availabilities\\AvailabilityPage.jsx", ["350"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\sessions\\SessionsPage.jsx", ["351", "352", "353", "354", "355", "356", "357", "358", "359", "360"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\appointments\\AppointmentsPage.jsx", ["361", "362", "363", "364", "365", "366", "367"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\messages\\MessagesPage.jsx", ["368", "369", "370", "371", "372", "373", "374", "375", "376", "377"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\reports\\ReportsPage.jsx", ["378", "379", "380"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\clients\\ClientsPage.jsx", ["381", "382", "383", "384", "385"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\components\\PermissionMatrix.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\ClientAppointmentsPage.jsx", ["386", "387", "388", "389", "390", "391", "392", "393", "394", "395"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\dashboard\\ClientDashboardPage.jsx", ["396", "397", "398", "399"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\ClientExpertsPage.jsx", ["400", "401", "402", "403", "404"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\messages\\ClientMessagesPage.jsx", ["405", "406", "407", "408", "409", "410", "411", "412", "413"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\packages\\ClientPackagesPage.jsx", ["414", "415"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\sessions\\ClientSessionsPage.jsx", ["416", "417", "418", "419", "420", "421", "422", "423", "424", "425"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\payments\\ClientPaymentsPage.jsx", ["426", "427", "428"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormTextarea.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormSelect.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormRadio.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormCheckbox.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormInput.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\common\\MultiSelect.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\ClientExpertDetailPage.jsx", ["429", "430", "431", "432", "433"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\BookAppointmentPage.jsx", ["434"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\notifications\\NotificationCenter.jsx", ["435", "436", "437"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\meeting\\MeetingPage.jsx", ["438", "439", "440"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\common\\ScrollToTop.jsx", [], [], {"ruleId": "441", "severity": 1, "message": "442", "line": 75, "column": 6, "nodeType": "443", "endLine": 75, "endColumn": 8, "suggestions": "444"}, {"ruleId": "445", "severity": 1, "message": "446", "line": 1, "column": 27, "nodeType": "447", "messageId": "448", "endLine": 1, "endColumn": 36}, {"ruleId": "445", "severity": 1, "message": "449", "line": 11, "column": 21, "nodeType": "447", "messageId": "448", "endLine": 11, "endColumn": 33}, {"ruleId": "450", "severity": 1, "message": "451", "line": 30, "column": 15, "nodeType": "452", "endLine": 30, "endColumn": 91}, {"ruleId": "450", "severity": 1, "message": "451", "line": 57, "column": 15, "nodeType": "452", "endLine": 57, "endColumn": 91}, {"ruleId": "450", "severity": 1, "message": "451", "line": 84, "column": 15, "nodeType": "452", "endLine": 84, "endColumn": 91}, {"ruleId": "445", "severity": 1, "message": "453", "line": 6, "column": 21, "nodeType": "447", "messageId": "448", "endLine": 6, "endColumn": 33}, {"ruleId": "445", "severity": 1, "message": "454", "line": 12, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 12, "endColumn": 19}, {"ruleId": "445", "severity": 1, "message": "455", "line": 14, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 14, "endColumn": 16}, {"ruleId": "445", "severity": 1, "message": "456", "line": 29, "column": 34, "nodeType": "447", "messageId": "448", "endLine": 29, "endColumn": 59}, {"ruleId": "445", "severity": 1, "message": "457", "line": 130, "column": 54, "nodeType": "447", "messageId": "448", "endLine": 130, "endColumn": 61}, {"ruleId": "445", "severity": 1, "message": "458", "line": 133, "column": 9, "nodeType": "447", "messageId": "448", "endLine": 133, "endColumn": 23}, {"ruleId": "445", "severity": 1, "message": "459", "line": 29, "column": 10, "nodeType": "447", "messageId": "448", "endLine": 29, "endColumn": 20}, {"ruleId": "445", "severity": 1, "message": "453", "line": 6, "column": 47, "nodeType": "447", "messageId": "448", "endLine": 6, "endColumn": 59}, {"ruleId": "445", "severity": 1, "message": "460", "line": 24, "column": 10, "nodeType": "447", "messageId": "448", "endLine": 24, "endColumn": 20}, {"ruleId": "445", "severity": 1, "message": "461", "line": 9, "column": 11, "nodeType": "447", "messageId": "448", "endLine": 9, "endColumn": 24}, {"ruleId": "445", "severity": 1, "message": "462", "line": 12, "column": 10, "nodeType": "447", "messageId": "448", "endLine": 12, "endColumn": 15}, {"ruleId": "445", "severity": 1, "message": "461", "line": 9, "column": 11, "nodeType": "447", "messageId": "448", "endLine": 9, "endColumn": 24}, {"ruleId": null, "fatal": true, "severity": 2, "message": "463", "line": 833, "column": 16, "nodeType": null}, {"ruleId": "445", "severity": 1, "message": "464", "line": 9, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 9, "endColumn": 14}, {"ruleId": "445", "severity": 1, "message": "465", "line": 11, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 11, "endColumn": 15}, {"ruleId": "445", "severity": 1, "message": "466", "line": 15, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 15, "endColumn": 11}, {"ruleId": "445", "severity": 1, "message": "467", "line": 16, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 16, "endColumn": 17}, {"ruleId": "445", "severity": 1, "message": "468", "line": 17, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 17, "endColumn": 16}, {"ruleId": "445", "severity": 1, "message": "469", "line": 18, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 18, "endColumn": 24}, {"ruleId": "445", "severity": 1, "message": "470", "line": 30, "column": 11, "nodeType": "447", "messageId": "448", "endLine": 30, "endColumn": 15}, {"ruleId": "445", "severity": 1, "message": "471", "line": 36, "column": 10, "nodeType": "447", "messageId": "448", "endLine": 36, "endColumn": 27}, {"ruleId": "445", "severity": 1, "message": "472", "line": 36, "column": 29, "nodeType": "447", "messageId": "448", "endLine": 36, "endColumn": 49}, {"ruleId": "441", "severity": 1, "message": "473", "line": 49, "column": 6, "nodeType": "443", "endLine": 49, "endColumn": 8, "suggestions": "474"}, {"ruleId": "445", "severity": 1, "message": "475", "line": 4, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 4, "endColumn": 14}, {"ruleId": "445", "severity": 1, "message": "476", "line": 7, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 7, "endColumn": 11}, {"ruleId": "445", "severity": 1, "message": "465", "line": 11, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 11, "endColumn": 15}, {"ruleId": "445", "severity": 1, "message": "467", "line": 15, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 15, "endColumn": 17}, {"ruleId": "445", "severity": 1, "message": "468", "line": 16, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 16, "endColumn": 16}, {"ruleId": "445", "severity": 1, "message": "469", "line": 17, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 17, "endColumn": 24}, {"ruleId": "445", "severity": 1, "message": "470", "line": 29, "column": 11, "nodeType": "447", "messageId": "448", "endLine": 29, "endColumn": 15}, {"ruleId": "445", "severity": 1, "message": "476", "line": 9, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 9, "endColumn": 11}, {"ruleId": "445", "severity": 1, "message": "477", "line": 13, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 13, "endColumn": 17}, {"ruleId": "445", "severity": 1, "message": "478", "line": 18, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 18, "endColumn": 12}, {"ruleId": "445", "severity": 1, "message": "479", "line": 20, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 20, "endColumn": 12}, {"ruleId": "445", "severity": 1, "message": "480", "line": 21, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 21, "endColumn": 12}, {"ruleId": "445", "severity": 1, "message": "481", "line": 27, "column": 10, "nodeType": "447", "messageId": "448", "endLine": 27, "endColumn": 14}, {"ruleId": "445", "severity": 1, "message": "482", "line": 43, "column": 10, "nodeType": "447", "messageId": "448", "endLine": 43, "endColumn": 21}, {"ruleId": "441", "severity": 1, "message": "483", "line": 276, "column": 6, "nodeType": "443", "endLine": 276, "endColumn": 23, "suggestions": "484"}, {"ruleId": "441", "severity": 1, "message": "485", "line": 281, "column": 6, "nodeType": "443", "endLine": 281, "endColumn": 8, "suggestions": "486"}, {"ruleId": "441", "severity": 1, "message": "483", "line": 342, "column": 6, "nodeType": "443", "endLine": 342, "endColumn": 28, "suggestions": "487"}, {"ruleId": "445", "severity": 1, "message": "476", "line": 13, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 13, "endColumn": 11}, {"ruleId": "445", "severity": 1, "message": "488", "line": 15, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 15, "endColumn": 28}, {"ruleId": "445", "severity": 1, "message": "470", "line": 256, "column": 11, "nodeType": "447", "messageId": "448", "endLine": 256, "endColumn": 15}, {"ruleId": "445", "severity": 1, "message": "489", "line": 11, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 11, "endColumn": 20}, {"ruleId": "445", "severity": 1, "message": "490", "line": 12, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 12, "endColumn": 17}, {"ruleId": "445", "severity": 1, "message": "476", "line": 13, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 13, "endColumn": 11}, {"ruleId": "445", "severity": 1, "message": "470", "line": 26, "column": 11, "nodeType": "447", "messageId": "448", "endLine": 26, "endColumn": 15}, {"ruleId": "445", "severity": 1, "message": "491", "line": 72, "column": 9, "nodeType": "447", "messageId": "448", "endLine": 72, "endColumn": 19}, {"ruleId": "445", "severity": 1, "message": "465", "line": 11, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 11, "endColumn": 15}, {"ruleId": "445", "severity": 1, "message": "492", "line": 12, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 12, "endColumn": 26}, {"ruleId": "445", "severity": 1, "message": "489", "line": 14, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 14, "endColumn": 20}, {"ruleId": "445", "severity": 1, "message": "467", "line": 15, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 15, "endColumn": 17}, {"ruleId": "445", "severity": 1, "message": "468", "line": 16, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 16, "endColumn": 16}, {"ruleId": "445", "severity": 1, "message": "469", "line": 17, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 17, "endColumn": 24}, {"ruleId": "445", "severity": 1, "message": "493", "line": 19, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 19, "endColumn": 28}, {"ruleId": "445", "severity": 1, "message": "470", "line": 31, "column": 11, "nodeType": "447", "messageId": "448", "endLine": 31, "endColumn": 15}, {"ruleId": "445", "severity": 1, "message": "494", "line": 151, "column": 9, "nodeType": "447", "messageId": "448", "endLine": 151, "endColumn": 24}, {"ruleId": "445", "severity": 1, "message": "491", "line": 171, "column": 9, "nodeType": "447", "messageId": "448", "endLine": 171, "endColumn": 19}, {"ruleId": "445", "severity": 1, "message": "465", "line": 5, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 5, "endColumn": 15}, {"ruleId": "445", "severity": 1, "message": "454", "line": 12, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 12, "endColumn": 19}, {"ruleId": "445", "severity": 1, "message": "455", "line": 14, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 14, "endColumn": 16}, {"ruleId": "445", "severity": 1, "message": "456", "line": 28, "column": 34, "nodeType": "447", "messageId": "448", "endLine": 28, "endColumn": 59}, {"ruleId": "445", "severity": 1, "message": "495", "line": 6, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 6, "endColumn": 18}, {"ruleId": "445", "severity": 1, "message": "496", "line": 10, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 10, "endColumn": 29}, {"ruleId": "445", "severity": 1, "message": "497", "line": 12, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 12, "endColumn": 18}, {"ruleId": "445", "severity": 1, "message": "498", "line": 13, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 13, "endColumn": 16}, {"ruleId": "445", "severity": 1, "message": "470", "line": 24, "column": 11, "nodeType": "447", "messageId": "448", "endLine": 24, "endColumn": 15}, {"ruleId": "445", "severity": 1, "message": "477", "line": 13, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 13, "endColumn": 17}, {"ruleId": "445", "severity": 1, "message": "499", "line": 15, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 15, "endColumn": 12}, {"ruleId": "445", "severity": 1, "message": "500", "line": 17, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 17, "endColumn": 24}, {"ruleId": "445", "severity": 1, "message": "479", "line": 20, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 20, "endColumn": 12}, {"ruleId": "445", "severity": 1, "message": "480", "line": 21, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 21, "endColumn": 12}, {"ruleId": "445", "severity": 1, "message": "482", "line": 45, "column": 10, "nodeType": "447", "messageId": "448", "endLine": 45, "endColumn": 21}, {"ruleId": "441", "severity": 1, "message": "483", "line": 280, "column": 6, "nodeType": "443", "endLine": 280, "endColumn": 23, "suggestions": "501"}, {"ruleId": "441", "severity": 1, "message": "485", "line": 285, "column": 6, "nodeType": "443", "endLine": 285, "endColumn": 8, "suggestions": "502"}, {"ruleId": "441", "severity": 1, "message": "483", "line": 347, "column": 6, "nodeType": "443", "endLine": 347, "endColumn": 28, "suggestions": "503"}, {"ruleId": "445", "severity": 1, "message": "470", "line": 20, "column": 11, "nodeType": "447", "messageId": "448", "endLine": 20, "endColumn": 15}, {"ruleId": "450", "severity": 1, "message": "451", "line": 487, "column": 19, "nodeType": "452", "endLine": 490, "endColumn": 20}, {"ruleId": "445", "severity": 1, "message": "464", "line": 9, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 9, "endColumn": 14}, {"ruleId": "445", "severity": 1, "message": "504", "line": 10, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 10, "endColumn": 11}, {"ruleId": "445", "severity": 1, "message": "465", "line": 11, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 11, "endColumn": 15}, {"ruleId": "445", "severity": 1, "message": "489", "line": 14, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 14, "endColumn": 20}, {"ruleId": "445", "severity": 1, "message": "466", "line": 15, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 15, "endColumn": 11}, {"ruleId": "445", "severity": 1, "message": "468", "line": 17, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 17, "endColumn": 16}, {"ruleId": "445", "severity": 1, "message": "470", "line": 31, "column": 11, "nodeType": "447", "messageId": "448", "endLine": 31, "endColumn": 15}, {"ruleId": "441", "severity": 1, "message": "473", "line": 80, "column": 6, "nodeType": "443", "endLine": 80, "endColumn": 8, "suggestions": "505"}, {"ruleId": "445", "severity": 1, "message": "506", "line": 164, "column": 9, "nodeType": "447", "messageId": "448", "endLine": 164, "endColumn": 14}, {"ruleId": "445", "severity": 1, "message": "494", "line": 233, "column": 9, "nodeType": "447", "messageId": "448", "endLine": 233, "endColumn": 24}, {"ruleId": "445", "severity": 1, "message": "507", "line": 7, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 7, "endColumn": 18}, {"ruleId": "445", "severity": 1, "message": "470", "line": 25, "column": 11, "nodeType": "447", "messageId": "448", "endLine": 25, "endColumn": 15}, {"ruleId": "445", "severity": 1, "message": "508", "line": 234, "column": 9, "nodeType": "447", "messageId": "448", "endLine": 234, "endColumn": 23}, {"ruleId": "445", "severity": 1, "message": "498", "line": 11, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 11, "endColumn": 16}, {"ruleId": "445", "severity": 1, "message": "509", "line": 15, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 15, "endColumn": 15}, {"ruleId": "445", "severity": 1, "message": "499", "line": 16, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 16, "endColumn": 12}, {"ruleId": "445", "severity": 1, "message": "510", "line": 17, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 17, "endColumn": 15}, {"ruleId": "445", "severity": 1, "message": "470", "line": 27, "column": 11, "nodeType": "447", "messageId": "448", "endLine": 27, "endColumn": 15}, {"ruleId": "441", "severity": 1, "message": "511", "line": 39, "column": 6, "nodeType": "443", "endLine": 39, "endColumn": 26, "suggestions": "512"}, {"ruleId": "445", "severity": 1, "message": "513", "line": 5, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 5, "endColumn": 12}, {"ruleId": "445", "severity": 1, "message": "476", "line": 9, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 9, "endColumn": 11}, {"ruleId": "445", "severity": 1, "message": "514", "line": 13, "column": 10, "nodeType": "447", "messageId": "448", "endLine": 13, "endColumn": 16}, {"ruleId": "445", "severity": 1, "message": "461", "line": 24, "column": 17, "nodeType": "447", "messageId": "448", "endLine": 24, "endColumn": 30}, {"ruleId": "445", "severity": 1, "message": "515", "line": 39, "column": 10, "nodeType": "447", "messageId": "448", "endLine": 39, "endColumn": 21}, {"ruleId": "441", "severity": 1, "message": "516", "line": 85, "column": 6, "nodeType": "443", "endLine": 85, "endColumn": 31, "suggestions": "517"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUserPermissions'. Either include it or remove the dependency array.", "ArrayExpression", ["518"], "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'registerUser' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'FormCheckbox' is defined but never used.", "'DocumentTextIcon' is defined but never used.", "'ArrowDownIcon' is defined but never used.", "'setHasUnreadNotifications' is assigned a value but never used.", "'clients' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", "'expertData' is assigned a value but never used.", "'clientData' is assigned a value but never used.", "'hasPermission' is assigned a value but never used.", "'pages' is assigned a value but never used.", "Parsing error: Expected corresponding JSX closing tag for <div>. (833:16)", "'XCircleIcon' is defined but never used.", "'ChartBarIcon' is defined but never used.", "'StarIcon' is defined but never used.", "'PlayCircleIcon' is defined but never used.", "'PaperClipIcon' is defined but never used.", "'DocumentArrowDownIcon' is defined but never used.", "'user' is assigned a value but never used.", "'isCreatingMeeting' is assigned a value but never used.", "'setIsCreatingMeeting' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadSessions'. Either include it or remove the dependency array.", ["519"], "'VideoCamera' is defined but never used.", "'UserIcon' is defined but never used.", "'UserCircleIcon' is defined but never used.", "'ClockIcon' is defined but never used.", "'XMarkIcon' is defined but never used.", "'TrashIcon' is defined but never used.", "'Link' is defined but never used.", "'typingUsers' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'markConversationAsRead'. Either include it or remove the dependency array.", ["520"], "React Hook useEffect has a missing dependency: 'loadConversations'. Either include it or remove the dependency array.", ["521"], ["522"], "'PresentationChartLineIcon' is defined but never used.", "'ArrowDownTrayIcon' is defined but never used.", "'CheckBadgeIcon' is defined but never used.", "'formatDate' is assigned a value but never used.", "'ChatBubbleLeftRightIcon' is defined but never used.", "'AdjustmentsHorizontalIcon' is defined but never used.", "'getStatusBorder' is assigned a value but never used.", "'ChevronDownIcon' is defined but never used.", "'ChatBubbleLeftEllipsisIcon' is defined but never used.", "'AcademicCapIcon' is defined but never used.", "'UserGroupIcon' is defined but never used.", "'PhoneIcon' is defined but never used.", "'InformationCircleIcon' is defined but never used.", ["523"], ["524"], ["525"], "'BellIcon' is defined but never used.", ["526"], "'today' is assigned a value but never used.", "'CheckCircleIcon' is defined but never used.", "'formatDateTime' is assigned a value but never used.", "'GlobeAltIcon' is defined but never used.", "'EnvelopeIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadExpertAndAvailability'. Either include it or remove the dependency array.", ["527"], "'CheckIcon' is defined but never used.", "'format' is defined but never used.", "'isInitiator' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'cleanup' and 'initializeConnection'. Either include them or remove the dependency array.", ["528"], {"desc": "529", "fix": "530"}, {"desc": "531", "fix": "532"}, {"desc": "533", "fix": "534"}, {"desc": "535", "fix": "536"}, {"desc": "537", "fix": "538"}, {"desc": "533", "fix": "539"}, {"desc": "535", "fix": "540"}, {"desc": "537", "fix": "541"}, {"desc": "531", "fix": "542"}, {"desc": "543", "fix": "544"}, {"desc": "545", "fix": "546"}, "Update the dependencies array to be: [fetchUserPermissions]", {"range": "547", "text": "548"}, "Update the dependencies array to be: [loadSessions]", {"range": "549", "text": "550"}, "Update the dependencies array to be: [markConversationAsRead, socket, user.id]", {"range": "551", "text": "552"}, "Update the dependencies array to be: [loadConversations]", {"range": "553", "text": "554"}, "Update the dependencies array to be: [markConversationAsRead, selectedConversation]", {"range": "555", "text": "556"}, {"range": "557", "text": "552"}, {"range": "558", "text": "554"}, {"range": "559", "text": "556"}, {"range": "560", "text": "550"}, "Update the dependencies array to be: [expertId, loadExpertAndAvailability, navigate]", {"range": "561", "text": "562"}, "Update the dependencies array to be: [user.id, appointmentId, initializeConnection, cleanup]", {"range": "563", "text": "564"}, [2597, 2599], "[fetchUserPermissions]", [1323, 1325], "[loadSessions]", [10531, 10548], "[markConversationAsRead, socket, user.id]", [10646, 10648], "[loadConversations]", [13050, 13072], "[markConversationAsRead, selectedConversation]", [10699, 10716], [10814, 10816], [13321, 13343], [2336, 2338], [1208, 1228], "[expertId, loadExpertAndAvailability, navigate]", [2679, 2704], "[user.id, appointmentId, initializeConnection, cleanup]"]