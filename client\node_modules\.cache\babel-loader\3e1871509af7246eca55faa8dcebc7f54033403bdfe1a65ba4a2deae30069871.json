{"ast": null, "code": "var _jsxFileName = \"C:\\\\Projeler\\\\kidgarden\\\\burky_root_web\\\\client\\\\src\\\\index.jsx\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { BrowserRouter } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { Toaster } from 'react-hot-toast';\nimport App from './App';\nimport './index.css';\n\n// Create a client for React Query\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000,\n      // 5 minutes\n      retry: 1\n    }\n  }\n});\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n/*#__PURE__*/\n// StrictMode geçici olarak devre dışı - P2P WebRTC için sorun yaratıyor\n// <React.StrictMode>\n_jsxDEV(BrowserRouter, {\n  children: /*#__PURE__*/_jsxDEV(QueryClientProvider, {\n    client: queryClient,\n    children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n      children: [/*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Toaster, {\n        position: \"top-right\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 7\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 25,\n  columnNumber: 5\n}, this)\n// </React.StrictMode>\n);", "map": {"version": 3, "names": ["React", "ReactDOM", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "QueryClient", "QueryClientProvider", "<PERSON>th<PERSON><PERSON><PERSON>", "Toaster", "App", "jsxDEV", "_jsxDEV", "queryClient", "defaultOptions", "queries", "refetchOnWindowFocus", "staleTime", "retry", "root", "createRoot", "document", "getElementById", "render", "children", "client", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position"], "sources": ["C:/Projeler/kidgarden/burky_root_web/client/src/index.jsx"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { BrowserRouter } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { Toaster } from 'react-hot-toast';\nimport App from './App';\nimport './index.css';\n\n// Create a client for React Query\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000, // 5 minutes\n      retry: 1,\n    },\n  },\n});\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n  // StrictMode geçici olarak devre dışı - P2P WebRTC için sorun yaratıyor\n  // <React.StrictMode>\n    <BrowserRouter>\n      <QueryClientProvider client={queryClient}>\n        <AuthProvider>\n          <App />\n          <Toaster position=\"top-right\" />\n        </AuthProvider>\n\n      </QueryClientProvider>\n    </BrowserRouter>\n  // </React.StrictMode>\n);\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,uBAAuB;AACxE,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAO,aAAa;;AAEpB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAG,IAAIP,WAAW,CAAC;EAClCQ,cAAc,EAAE;IACdC,OAAO,EAAE;MACPC,oBAAoB,EAAE,KAAK;MAC3BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;MAAE;MAC1BC,KAAK,EAAE;IACT;EACF;AACF,CAAC,CAAC;AAEF,MAAMC,IAAI,GAAGf,QAAQ,CAACgB,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AACjEH,IAAI,CAACI,MAAM;AAAA;AACT;AACA;AACEX,OAAA,CAACP,aAAa;EAAAmB,QAAA,eACZZ,OAAA,CAACL,mBAAmB;IAACkB,MAAM,EAAEZ,WAAY;IAAAW,QAAA,eACvCZ,OAAA,CAACJ,YAAY;MAAAgB,QAAA,gBACXZ,OAAA,CAACF,GAAG;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACPjB,OAAA,CAACH,OAAO;QAACqB,QAAQ,EAAC;MAAW;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEI;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACT;AACjB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}